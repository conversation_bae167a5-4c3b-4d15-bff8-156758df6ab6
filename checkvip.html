<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>会员积分余额检查</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .controls {
            margin-bottom: 20px;
            text-align: center;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 0 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .loading {
            text-align: center;
            color: #666;
            margin: 20px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
            vertical-align: top;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            position: sticky;
            top: 0;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        .status-success {
            color: #28a745;
            font-weight: bold;
        }
        .status-error {
            color: #dc3545;
            font-weight: bold;
        }
        .cookie-info {
            word-break: break-all;
            font-size: 12px;
            line-height: 1.3;
        }
        .api-response {
            word-break: break-word;
            font-size: 16px;
            line-height: 1.4;
        }
        .session-info {
            background-color: #e9ecef;
            padding: 8px;
            border-radius: 4px;
            margin: 4px 0;
            font-size: 11px;
        }
        .error-message {
            color: #dc3545;
            font-style: italic;
        }
        .progress-info {
            text-align: center;
            color: #007bff;
            font-weight: bold;
            margin: 10px 0;
        }
        details {
            margin: 4px 0;
        }
        summary {
            font-size: 12px;
            padding: 2px 4px;
            background-color: #f8f9fa;
            border-radius: 3px;
        }
        summary:hover {
            background-color: #e9ecef;
        }
        pre {
            background-color: #f8f9fa;
            padding: 8px;
            border-radius: 4px;
            overflow-x: auto;
            max-height: 200px;
        }
        .worker-url-container {
            margin-bottom: 15px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .worker-url-container label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }

        /* Toast 样式 */
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #28a745;
            color: white;
            padding: 15px 20px;
            border-radius: 5px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 1000;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        }

        .toast.show {
            opacity: 1;
            transform: translateX(0);
        }

        .toast.error {
            background-color: #dc3545;
        }

        .toast.info {
            background-color: #007bff;
        }

        /* 查看详情按钮样式 */
        .view-link {
            display: inline-block;
            padding: 4px 8px;
            background-color: #007bff;
            color: white !important;
            text-decoration: none;
            border-radius: 3px;
            font-size: 11px;
            font-weight: bold;
            transition: background-color 0.2s ease;
        }

        .view-link:hover {
            background-color: #0056b3;
            text-decoration: none;
        }

        /* 新增的CSS类 */
        .check-button-container {
            text-align: center;
            margin: 20px 0;
        }

        .hidden {
            display: none;
        }

        .table-header-seq { width: 60px; }
        .table-header-user { width: 120px; }
        .table-header-credits { width: 400px; }
        .table-header-token { width: 300px; }
        .table-header-time { width: 120px; }
        .table-header-status { width: 80px; }

        .token-header {
            padding: 6px;
            border-radius: 4px;
            margin-bottom: 8px;
            border-left: 4px solid;
        }

        .token-header-success {
            background-color: #e8f5e8;
            border-left-color: #28a745;
        }

        .token-header-error {
            background-color: #ffe6e6;
            border-left-color: #dc3545;
        }

        .token-status-success {
            color: #28a745;
            font-weight: bold;
        }

        .token-status-error {
            color: #dc3545;
            font-weight: bold;
        }

        .credit-blocks-container {
            max-height: 150px;
            overflow-y: auto;
            display: flex;
            /* flex-wrap: wrap; */
            gap: 6px;
        }

        .credit-block {
            padding: 4px;
            border: 1px solid #ddd;
            border-radius: 3px;
            flex: 1;
            min-width: calc(50% - 3px);
            max-width: calc(50% - 3px);
        }

        .credit-block-active {
            background-color: #f0f8ff;
        }

        .credit-block-inactive {
            background-color: #f8f8f8;
        }

        .credit-block-title {
            font-size: 14px;
        }

        .credit-block-balance-active {
            color: #28a745;
        }

        .credit-block-balance-inactive {
            color: #999;
        }

        .credit-block-details {
            font-size: 12px;
            color: #666;
            line-height: 1.2;
        }



        .more-blocks-details {
            margin-top: 4px;
        }

        .more-blocks-summary {
            cursor: pointer;
            color: #666;
            font-size: 12px;
            padding: 2px;
        }

        .more-blocks-content {
            margin-top: 2px;
        }

        .token-result-container {
            font-size: 15px;
            line-height: 1.3;
            margin-bottom: 15px;
            padding-left: 10px;
        }

        .balance-display {
            margin-bottom: 8px;
            padding: 8px;
            background-color: #f0f8ff;
            border-radius: 4px;
            text-align: center;
        }

        .balance-amount {
            color: #28a745;
            font-size: 18px;
            font-weight: bold;
        }

        .credit-blocks-header {
            margin-bottom: 6px;
            font-size: 14px;
            color: #666;
        }

        .full-response-details {
            margin-top: 8px;
        }

        .full-response-summary {
            cursor: pointer;
            color: #007bff;
            font-size: 12px;
            padding: 3px;
        }

        .full-response-content {
            margin-top: 4px;
            padding: 6px;
            background-color: #f8f9fa;
            border-radius: 3px;
            max-height: 120px;
            overflow-y: auto;
        }

        .full-response-pre {
            font-size: 11px;
            margin: 0;
            background: none;
            padding: 0;
        }

        .token-error-container {
            padding-left: 10px;
            margin-bottom: 15px;
        }

        .tokens-display-container {
            font-size: 11px;
            word-break: break-all;
            line-height: 1.3;
        }

        .tokens-header {
            margin-bottom: 4px;
            font-weight: bold;
            color: #333;
        }

        .token-item {
            margin-bottom: 8px;
            padding: 4px;
            border: 1px solid #ddd;
            border-radius: 3px;
            background-color: #f8f9fa;
        }

        .token-item-header {
            font-weight: bold;
            color: #666;
            margin-bottom: 2px;
        }

        .token-item-content {
            background-color: #fff;
            padding: 3px;
            border-radius: 2px;
            max-height: 60px;
            overflow-y: auto;
            border: 1px solid #e0e0e0;
        }

        .token-item-link {
            margin-top: 4px;
        }


        .user-cell {
            word-break: break-word;
        }

        .api-response-cell {
            max-width: 400px;
        }

        .token-cell {
            max-width: 300px;
        }

        .time-cell {
            font-size: 12px;
        }

        /* Pool 账号相关样式 */
        .pool-account-section {
            margin: 20px 0;
            padding: 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border: 1px solid #e2e8f0;
        }

        .pool-account-section h3 {
            margin: 0 0 16px 0;
            color: #2d3748;
            font-size: 18px;
            font-weight: 600;
        }

        .pool-account-info {
            background: #f7fafc;
            border-radius: 8px;
            padding: 16px;
        }

        .pool-account-item {
            padding: 12px 0;
            border-bottom: 1px solid #e2e8f0;
        }

        .pool-account-item:last-child {
            border-bottom: none;
        }

        .pool-account-key {
            font-weight: 500;
            color: #2d3748;
            font-size: 14px;
            margin-bottom: 8px;
        }

        .pool-account-value {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .pool-value-text {
            font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
            background: white;
            padding: 8px 12px;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
            font-size: 13px;
            color: #4a5568;
            flex: 1;
            word-break: break-all;
            white-space: pre-wrap;
        }

        .pool-copy-btn {
            background: #48bb78;
            color: white;
            border: none;
            padding: 6px 10px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.2s;
        }

        .pool-copy-btn:hover {
            background: #38a169;
        }

        /* 总余额显示样式 */
        .total-balance-display {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
            margin: 8px 0;
        }

        .total-balance-amount {
            font-size: 28px;
            font-weight: 700;
            margin-left: 8px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>会员积分余额检查工具</h1>
        
        <div class="controls">
            <div class="check-button-container">
                <button id="checkBtn" onclick="checkAllVIP()">🚀 检查会员积分余额</button>
                <button id="checkPublicBtn" onclick="checkPublicVIP()" style="background-color: #28a745; margin-left: 10px;">🏥 检查公益号池积分</button>
                <button id="fetchPoolBtn" onclick="fetchPoolAccount()" style="background-color: #667eea; margin-left: 10px;">🎲 随机获取账号</button>
                <button id="testUseageBtn" onclick="testGetAccountsWithUseage()" style="background-color: #48bb78; margin-left: 10px;">🔍 获取有useage的账号</button>
            </div>
        </div>

        <div id="loading" class="loading hidden">
            正在检查中，请稍候...
        </div>

        <!-- 随机账号显示区域 -->
        <div id="poolAccountSection" class="pool-account-section hidden">
            <h3>随机获取的账号信息</h3>
            <div id="poolAccountInfo" class="pool-account-info"></div>
        </div>

        <table id="resultsTable" class="hidden">
            <thead>
                <tr>
                    <th class="table-header-seq">序号</th>
                    <th class="table-header-user">用户</th>
                    <th class="table-header-credits">积分信息</th>
                    <th class="table-header-token">Token</th>
                    <th class="table-header-time">时间</th>
                    <th class="table-header-status">状态</th>
                </tr>
            </thead>
            <tbody id="resultsBody">
            </tbody>
        </table>
    </div>

    <script>

        
            
        // 数据数组 - 通过接口动态获取
        let arr = [];



        // 提取token值并生成查看链接
        function extractTokenAndGenerateViewLink(tokenUrl) {
            try {
                // 清理URL，去除前后空格
                const cleanUrl = tokenUrl.trim();

                // 从URL中提取token参数值
                const urlParts = cleanUrl.split('?');
                if (urlParts.length < 2) {
                    return null;
                }

                const urlParams = new URLSearchParams(urlParts[1]);
                const tokenValue = urlParams.get('token');

                if (tokenValue) {
                    return `https://portal.withorb.com/view?token=${tokenValue}`;
                }
                return null;
            } catch (error) {
                console.error('提取token失败:', error);
                return null;
            }
        }

        // Toast 提示功能
        function showToast(message, type = 'success') {
            // 移除已存在的 toast
            const existingToast = document.querySelector('.toast');
            if (existingToast) {
                existingToast.remove();
            }

            // 创建新的 toast
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.textContent = message;

            document.body.appendChild(toast);

            // 显示 toast
            setTimeout(() => {
                toast.classList.add('show');
            }, 100);

            // 3秒后自动隐藏
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }

        // 使用 Cloudflare Workers 代理请求，将 token 作为参数传递
        async function fetchCredits(token) {
            const workerUrl = 'https://checkvip.767700.xyz'; // 固定的 Workers URL

            try {
                console.log('使用 Cloudflare Workers 代理:', workerUrl);
                console.log('Token:', token);

                // 构建请求 URL，将 token 作为查询参数
                const requestUrl = `${workerUrl}?token=${encodeURIComponent(token.trim())}`;
                console.log('请求 URL:', requestUrl);

                const response = await fetch(requestUrl, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
                    }
                });

                console.log('Workers 响应状态:', response.status, response.statusText);

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('Workers 错误响应:', errorText);
                    throw new Error(`Workers 请求失败: ${response.status} ${response.statusText}\n${errorText}`);
                }

                const result = await response.json();
                console.log('Workers 返回结果:', result);

                // 检查 Workers 返回的结果
                if (result.error) {
                    throw new Error(`API 错误: ${result.error}${result.message ? ' - ' + result.message : ''}`);
                }

                if (!result.ok) {
                    throw new Error(`API 请求失败: ${result.status} ${result.statusText || result.error || '未知错误'}`);
                }

                console.log('Workers 代理请求成功，返回数据:', result.data);
                return result.data;

            } catch (error) {
                console.error('Workers 代理请求失败:', error);
                throw error;
            }
        }

        // 检查所有 VIP 状态
        async function checkAllVIP() {
            const checkBtn = document.getElementById('checkBtn');
            const loading = document.getElementById('loading');
            const table = document.getElementById('resultsTable');
            const tbody = document.getElementById('resultsBody');

            // 验证数据数组
            if (!arr || arr.length === 0) {
                showToast('没有找到要检查的数据，请在代码中添加 token 数据', 'error');
                return;
            }

            checkBtn.disabled = true;
            loading.innerHTML = '正在检查中，请稍候...';
            loading.classList.remove('hidden');
            table.classList.add('hidden');
            tbody.innerHTML = '';

            console.log(`开始检查 ${arr.length} 个用户的积分状态`);
            console.log('使用 Workers URL: https://checkvip.767700.xyz');

            for (let i = 0; i < arr.length; i++) {
                const item = arr[i];

                // 更新进度
                loading.innerHTML = `正在检查第 ${i + 1}/${arr.length} 个用户: ${item.user}`;

                const row = document.createElement('tr');

                let apiResponse = '';
                let requestStatus = '';
                let allCredits = [];
                let hasError = false;
                let errorMessages = [];

                // 遍历用户的所有 tokens
                for (let tokenIndex = 0; tokenIndex < item.tokens.length; tokenIndex++) {
                    const token = item.tokens[tokenIndex];

                    try {
                        console.log(`检查用户 ${i + 1}: ${item.user} - Token ${tokenIndex + 1}/${item.tokens.length}`);
                        const credits = await fetchCredits(token);
                        allCredits.push({
                            tokenIndex: tokenIndex + 1,
                            token: token,
                            credits: credits,
                            success: true
                        });
                    } catch (error) {
                        console.error(`用户 ${i + 1} Token ${tokenIndex + 1} 检查失败:`, error);
                        allCredits.push({
                            tokenIndex: tokenIndex + 1,
                            token: token,
                            error: error.message,
                            success: false
                        });
                        hasError = true;
                        errorMessages.push(`Token ${tokenIndex + 1}: ${error.message}`);
                    }

                    // 添加延迟避免请求过快
                    if (tokenIndex < item.tokens.length - 1) {
                        await new Promise(resolve => setTimeout(resolve, 500));
                    }
                }

                // 构建 API 响应显示
                if (allCredits.length > 0) {
                    let responseHtml = '';

                    // 遍历所有 token 的结果
                    allCredits.forEach((result, index) => {
                        const tokenHeader = `
                            <div class="token-header ${result.success ? 'token-header-success' : 'token-header-error'}">
                                <strong>Token ${result.tokenIndex}:</strong>
                                <span class="${result.success ? 'token-status-success' : 'token-status-error'}">
                                    ${result.success ? '✅ 成功' : '❌ 失败'}
                                </span>
                            </div>
                        `;

                        if (result.success && result.credits) {
                            const credits = result.credits;
                            const creditsBalance = credits.credits_balance || '0.00';
                            const creditBlocks = credits.credit_blocks || [];

                            // 构建积分块信息 - 默认只显示前两个块
                            let visibleBlocksInfo = '';
                            let hiddenBlocksInfo = '';

                            creditBlocks.forEach((block, blockIndex) => {
                                const balance = block.balance || 'null';
                                const effectiveDate = block.effective_date ? new Date(block.effective_date).toLocaleDateString() : 'N/A';
                                const expiryDate = block.expiry_date ? new Date(block.expiry_date).toLocaleDateString() : 'N/A';
                                const isActive = block.is_active ? '✅' : '❌';

                                const blockHtml = `
                                    <div class="credit-block ${block.is_active ? 'credit-block-active' : 'credit-block-inactive'}">
                                        <div class="credit-block-title"><strong>块${blockIndex + 1}:</strong> <span class="${block.balance ? 'credit-block-balance-active' : 'credit-block-balance-inactive'}">${balance}</span> ${isActive}</div>
                                        <div class="credit-block-details">
                                            ${effectiveDate} → ${expiryDate}<br>
                                            最大: ${block.maximum_initial_balance} | ID: ${block.id.substring(0, 8)}...
                                        </div>
                                    </div>
                                `;

                                // 前两个块默认显示，后面的块隐藏
                                if (blockIndex < 2) {
                                    visibleBlocksInfo += blockHtml;
                                } else {
                                    hiddenBlocksInfo += blockHtml;
                                }
                            });

                            // 构建完整的积分块显示
                            const blocksInfo = visibleBlocksInfo
                            
                            // + (hiddenBlocksInfo ? `
                            //     <details class="more-blocks-details">
                            //         <summary class="more-blocks-summary">显示更多积分块 (${creditBlocks.length - 2} 个)</summary>
                            //         <div class="more-blocks-content">
                            //             ${hiddenBlocksInfo}
                            //         </div>
                            //     </details>
                            // ` : '');

                            responseHtml += `
                                ${tokenHeader}
                                <div class="token-result-container">
                                    <div class="balance-display">
                                        <strong>余额:</strong> <span class="balance-amount">${creditsBalance}</span>
                                    </div>
                                    <div class="credit-blocks-header">
                                        <strong>积分块 (${creditBlocks.length} 个):</strong>
                                    </div>
                                    <div class="credit-blocks-container">
                                        ${blocksInfo}
                                    </div>
                                    <details class="full-response-details">
                                        <summary class="full-response-summary">查看完整响应</summary>
                                        <div class="full-response-content">
                                            <pre class="full-response-pre">${JSON.stringify(credits, null, 2)}</pre>
                                        </div>
                                    </details>
                                </div>
                            `;
                        } else if (!result.success) {
                            responseHtml += `
                                ${tokenHeader}
                                <div class="token-error-container">
                                    <span class="error-message">❌ ${result.error}</span>
                                </div>
                            `;
                        }
                    });

                    apiResponse = responseHtml;
                    requestStatus = hasError ?
                        `<span class="status-error">⚠️ 部分失败</span>` :
                        '<span class="status-success">✅ 全部成功</span>';

                    console.log(`用户 ${i + 1} 检查完成，成功: ${allCredits.filter(r => r.success).length}/${allCredits.length}`);
                } else {
                    apiResponse = `<span class="error-message">❌ 没有有效的 token</span>`;
                    requestStatus = '<span class="status-error">❌ 失败</span>';
                }

                // 构建 Token 显示
                let tokenDisplay = `
                    <div class="tokens-display-container">
                        <div class="tokens-header">
                            Tokens (${item.tokens.length} 个):
                        </div>
                `;

                // 遍历所有 tokens
                item.tokens.forEach((token, tokenIndex) => {
                    const viewLink = extractTokenAndGenerateViewLink(token);
                    tokenDisplay += `
                        <div class="token-item">
                            <div class="token-item-header">Token ${tokenIndex + 1}:</div>
                            <div class="token-item-content">
                                ${token.trim()}
                            </div>
                            ${viewLink ? `
                                <div class="token-item-link">
                                    <a href="${viewLink}" target="_blank" class="view-link view-link-small">
                                        🔗 查看详情 ${tokenIndex + 1}
                                    </a>
                                </div>
                            ` : ''}
                        </div>
                    `;
                });

                tokenDisplay += `</div>`;

                // 获取当前时间作为检查时间
                const displayTime = item.time || new Date().toLocaleString('zh-CN', {
                    timeZone: 'Asia/Shanghai',
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                });

                row.innerHTML = `
                    <td>${i + 1}</td>
                    <td class="user-cell">${item.user}</td>
                    <td class="api-response api-response-cell">${apiResponse}</td>
                    <td class="cookie-info token-cell">${tokenDisplay}</td>
                    <td class="time-cell">${displayTime}</td>
                    <td>${requestStatus}</td>
                `;

                tbody.appendChild(row);
                table.classList.remove('hidden');

                // 添加延迟避免请求过快
                if (i < arr.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
            }

            loading.classList.add('hidden');
            checkBtn.disabled = false;

            console.log('所有用户检查完成');

            // 显示完成提示
            const successCount = tbody.querySelectorAll('.status-success').length;
            const failCount = tbody.querySelectorAll('.status-error').length;
            showToast(`检查完成！成功: ${successCount} 个，失败: ${failCount} 个`, 'info');
        }

        // 检查公益号池 VIP 状态
        async function checkPublicVIP() {
            const checkBtn = document.getElementById('checkPublicBtn');
            const loading = document.getElementById('loading');
            const table = document.getElementById('resultsTable');
            const tbody = document.getElementById('resultsBody');

            // 验证数据数组
            if (!arr || arr.length === 0) {
                showToast('没有找到要检查的数据，请先获取账号数据', 'error');
                return;
            }

            // 过滤出公益号池
            const publicAccounts = arr.filter(item => item.user === '公益号池');

            if (publicAccounts.length === 0) {
                showToast('没有找到公益号池，无法执行检测', 'error');
                return;
            }

            checkBtn.disabled = true;
            checkBtn.textContent = '检查中...';
            loading.innerHTML = '正在检查公益号池，请稍候...';
            loading.classList.remove('hidden');
            table.classList.add('hidden');
            tbody.innerHTML = '';

            console.log(`开始检查 ${publicAccounts.length} 个公益号池的积分状态`);
            console.log('使用 Workers URL: https://checkvip.767700.xyz');

            for (let i = 0; i < publicAccounts.length; i++) {
                const item = publicAccounts[i];

                // 更新进度
                loading.innerHTML = `正在检查第 ${i + 1}/${publicAccounts.length} 个公益号池: ${item.user}`;

                const row = document.createElement('tr');

                let apiResponse = '';
                let requestStatus = '';
                let allCredits = [];
                let hasError = false;
                let errorMessages = [];

                // 遍历用户的所有 tokens
                for (let tokenIndex = 0; tokenIndex < item.tokens.length; tokenIndex++) {
                    const token = item.tokens[tokenIndex];

                    try {
                        console.log(`检查公益号池 ${i + 1}: ${item.user} - Token ${tokenIndex + 1}/${item.tokens.length}`);
                        const credits = await fetchCredits(token);
                        allCredits.push({
                            tokenIndex: tokenIndex + 1,
                            token: token,
                            credits: credits,
                            success: true
                        });
                    } catch (error) {
                        console.error(`公益号池 ${i + 1} Token ${tokenIndex + 1} 检查失败:`, error);
                        allCredits.push({
                            tokenIndex: tokenIndex + 1,
                            token: token,
                            error: error.message,
                            success: false
                        });
                        hasError = true;
                        errorMessages.push(`Token ${tokenIndex + 1}: ${error.message}`);
                    }

                    // 添加延迟避免请求过快
                    if (tokenIndex < item.tokens.length - 1) {
                        await new Promise(resolve => setTimeout(resolve, 500));
                    }
                }

                // 构建 API 响应显示（使用与原函数相同的逻辑）
                if (allCredits.length > 0) {
                    let responseHtml = '';

                    // 遍历所有 token 的结果
                    allCredits.forEach((result, index) => {
                        const tokenHeader = `
                            <div class="token-header ${result.success ? 'token-header-success' : 'token-header-error'}">
                                <strong>Token ${result.tokenIndex}:</strong>
                                <span class="${result.success ? 'token-status-success' : 'token-status-error'}">
                                    ${result.success ? '✅ 成功' : '❌ 失败'}
                                </span>
                            </div>
                        `;

                        if (result.success && result.credits) {
                            const credits = result.credits;
                            const creditsBalance = credits.credits_balance || '0.00';
                            const creditBlocks = credits.credit_blocks || [];

                            // 构建积分块信息 - 默认只显示前两个块
                            let visibleBlocksInfo = '';
                            let hiddenBlocksInfo = '';

                            creditBlocks.forEach((block, blockIndex) => {
                                const balance = block.balance || 'null';
                                const effectiveDate = block.effective_date ? new Date(block.effective_date).toLocaleDateString() : 'N/A';
                                const expiryDate = block.expiry_date ? new Date(block.expiry_date).toLocaleDateString() : 'N/A';
                                const isActive = block.is_active ? '✅' : '❌';

                                const blockHtml = `
                                    <div class="credit-block ${block.is_active ? 'credit-block-active' : 'credit-block-inactive'}">
                                        <div class="credit-block-title"><strong>块${blockIndex + 1}:</strong> <span class="${block.balance ? 'credit-block-balance-active' : 'credit-block-balance-inactive'}">${balance}</span> ${isActive}</div>
                                        <div class="credit-block-details">
                                            ${effectiveDate} → ${expiryDate}<br>
                                            最大: ${block.maximum_initial_balance} | ID: ${block.id.substring(0, 8)}...
                                        </div>
                                    </div>
                                `;

                                // 前两个块默认显示，后面的块隐藏
                                if (blockIndex < 2) {
                                    visibleBlocksInfo += blockHtml;
                                } else {
                                    hiddenBlocksInfo += blockHtml;
                                }
                            });

                            // 构建完整的积分块显示
                            const blocksInfo = visibleBlocksInfo;

                            responseHtml += `
                                ${tokenHeader}
                                <div class="token-result-container">
                                    <div class="balance-display">
                                        <strong>余额:</strong> <span class="balance-amount">${creditsBalance}</span>
                                    </div>
                                    <div class="credit-blocks-header">
                                        <strong>积分块 (${creditBlocks.length} 个):</strong>
                                    </div>
                                    <div class="credit-blocks-container">
                                        ${blocksInfo}
                                    </div>
                                    <details class="full-response-details">
                                        <summary class="full-response-summary">查看完整响应</summary>
                                        <div class="full-response-content">
                                            <pre class="full-response-pre">${JSON.stringify(credits, null, 2)}</pre>
                                        </div>
                                    </details>
                                </div>
                            `;
                        } else if (!result.success) {
                            responseHtml += `
                                ${tokenHeader}
                                <div class="token-error-container">
                                    <span class="error-message">❌ ${result.error}</span>
                                </div>
                            `;
                        }
                    });

                    apiResponse = responseHtml;
                    requestStatus = hasError ?
                        `<span class="status-error">⚠️ 部分失败</span>` :
                        '<span class="status-success">✅ 全部成功</span>';

                    console.log(`公益号池 ${i + 1} 检查完成，成功: ${allCredits.filter(r => r.success).length}/${allCredits.length}`);
                } else {
                    apiResponse = `<span class="error-message">❌ 没有有效的 token</span>`;
                    requestStatus = '<span class="status-error">❌ 失败</span>';
                }

                // 构建 Token 显示
                let tokenDisplay = `
                    <div class="tokens-display-container">
                        <div class="tokens-header">
                            Tokens (${item.tokens.length} 个):
                        </div>
                `;

                // 遍历所有 tokens
                item.tokens.forEach((token, tokenIndex) => {
                    const viewLink = extractTokenAndGenerateViewLink(token);
                    tokenDisplay += `
                        <div class="token-item">
                            <div class="token-item-header">Token ${tokenIndex + 1}:</div>
                            <div class="token-item-content">
                                ${token.trim()}
                            </div>
                            ${viewLink ? `
                                <div class="token-item-link">
                                    <a href="${viewLink}" target="_blank" class="view-link view-link-small">
                                        🔗 查看详情 ${tokenIndex + 1}
                                    </a>
                                </div>
                            ` : ''}
                        </div>
                    `;
                });

                tokenDisplay += `</div>`;

                // 获取当前时间作为检查时间
                const displayTime = item.time || new Date().toLocaleString('zh-CN', {
                    timeZone: 'Asia/Shanghai',
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                });

                row.innerHTML = `
                    <td>${i + 1}</td>
                    <td class="user-cell">${item.user}</td>
                    <td class="api-response api-response-cell">${apiResponse}</td>
                    <td class="cookie-info token-cell">${tokenDisplay}</td>
                    <td class="time-cell">${displayTime}</td>
                    <td>${requestStatus}</td>
                `;

                tbody.appendChild(row);
                table.classList.remove('hidden');

                // 添加延迟避免请求过快
                if (i < publicAccounts.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
            }

            loading.classList.add('hidden');
            checkBtn.disabled = false;
            checkBtn.textContent = '🏥 检查公益号池积分';

            console.log('公益号池检查完成');

            // 显示完成提示
            const successCount = tbody.querySelectorAll('.status-success').length;
            const failCount = tbody.querySelectorAll('.status-error').length;
            showToast(`公益号池检查完成！成功: ${successCount} 个，失败: ${failCount} 个`, 'info');
        }

        // ===== Pool 功能相关函数 =====

        // 随机获取账号的主函数
        async function fetchPoolAccount() {
            const button = document.getElementById('fetchPoolBtn');
            const poolSection = document.getElementById('poolAccountSection');
            const poolInfo = document.getElementById('poolAccountInfo');

            // 重置状态
            button.disabled = true;
            button.textContent = '获取中...';
            poolSection.classList.remove('hidden');
            poolInfo.innerHTML = '<div style="text-align: center; color: #666;">正在获取数据...</div>';

            try {
                const response = await fetch('https://account.767700.xyz?token=UBmkmNhDBb');

                if (!response.ok) {
                    throw new Error(`HTTP错误! 状态: ${response.status}`);
                }

                const responseData = await response.json();

                // 获取 result 的值
                if (!responseData.result) {
                    throw new Error('返回数据格式错误：缺少 result 字段');
                }

                // 将字符串转换为数组
                let accountArray;
                try {
                    let cleanedResult = responseData.result;

                    if (typeof cleanedResult === 'string') {
                        cleanedResult = cleanedResult.replace(/\n/g, '').replace(/\s+/g, ' ').trim();

                        const openBrackets = (cleanedResult.match(/\[/g) || []).length;
                        const closeBrackets = (cleanedResult.match(/\]/g) || []).length;

                        if (openBrackets > closeBrackets) {
                            const missingBrackets = openBrackets - closeBrackets;
                            cleanedResult += ']'.repeat(missingBrackets);
                        }

                        cleanedResult = cleanedResult
                            .replace(/,\s*}/g, '}')
                            .replace(/,\s*]/g, ']')
                            .replace(/}\s*,\s*{/g, '},{');
                    }

                    accountArray = JSON.parse(cleanedResult);

                } catch (e) {
                    throw new Error(`result 不是有效的JSON格式: ${e.message}`);
                }

                if (!Array.isArray(accountArray) || accountArray.length === 0) {
                    throw new Error('result 不是有效的数组或数组为空');
                }

                // 随机选择一个账号
                const randomIndex = Math.floor(Math.random() * accountArray.length);
                const selectedAccount = accountArray[randomIndex];

                // 先显示基本账号信息
                displayPoolAccount(selectedAccount, null);

                // 然后异步获取 useage 信息
                if (responseData.useage) {
                    updatePoolUsageStatus('处理中...');

                    try {
                        let useageUrls = [];
                        if (typeof responseData.useage === 'string') {
                            useageUrls = JSON.parse(responseData.useage);
                        } else if (Array.isArray(responseData.useage)) {
                            useageUrls = responseData.useage;
                        } else {
                            throw new Error('useage 格式不正确');
                        }

                        if (useageUrls.length > 0) {
                            await fetchPoolUsageFromUrls(useageUrls);
                        } else {
                            updatePoolUsageStatus('useage 数组为空');
                        }
                    } catch (error) {
                        updatePoolUsageStatus(`获取失败: ${error.message}`);
                    }
                }

            } catch (error) {
                poolInfo.innerHTML = `<div style="color: #e53e3e; text-align: center;">获取数据失败: ${error.message}</div>`;
            } finally {
                button.disabled = false;
                button.textContent = '🎲 随机获取账号';
            }
        }

        // 显示账号信息
        function displayPoolAccount(account, usageInfo = null) {
            const poolInfo = document.getElementById('poolAccountInfo');
            poolInfo.innerHTML = '';

            // 遍历账号对象的所有属性
            Object.entries(account).forEach(([key, value]) => {
                const itemDiv = document.createElement('div');
                itemDiv.className = 'pool-account-item';

                itemDiv.innerHTML = `
                    <div class="pool-account-key">${key}:</div>
                    <div class="pool-account-value">
                        <div class="pool-value-text">${value}</div>
                        <button class="pool-copy-btn" onclick="copyToClipboardPool('${value.replace(/'/g, "\\'")}')">复制</button>
                    </div>
                `;

                poolInfo.appendChild(itemDiv);
            });
        }

        // 更新 useage 状态显示
        function updatePoolUsageStatus(message) {
            const poolInfo = document.getElementById('poolAccountInfo');
            let usageDiv = poolInfo.querySelector('.useage-status');

            if (!usageDiv) {
                const itemDiv = document.createElement('div');
                itemDiv.className = 'pool-account-item';
                itemDiv.innerHTML = `
                    <div class="pool-account-key">useage:</div>
                    <div class="pool-account-value">
                        <div class="useage-status">${message}</div>
                    </div>
                `;
                poolInfo.appendChild(itemDiv);
            } else {
                usageDiv.innerHTML = message;
            }
        }

        // 处理 useage URL 数组
        async function fetchPoolUsageFromUrls(useageUrls) {
            const useageResults = [];
            let totalBalance = 0;

            for (let i = 0; i < useageUrls.length; i++) {
                const url = useageUrls[i];

                try {
                    const useageData = await fetchPoolUsageDetail(url);

                    const result = {
                        index: i + 1,
                        url: url,
                        data: useageData,
                        success: true
                    };
                    useageResults.push(result);

                    const creditsBalance = parseFloat(useageData.credits_balance) || 0;
                    totalBalance += creditsBalance;

                } catch (error) {
                    useageResults.push({
                        index: i + 1,
                        url: url,
                        error: error.message,
                        success: false
                    });
                }

                if (i < useageUrls.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, 500));
                }
            }

            displayPoolFinalUsageBalance(totalBalance, useageResults);
        }

        // 获取单个 useage URL 的详细信息
        async function fetchPoolUsageDetail(url) {
            try {
                const cleanUrl = url.trim();
                const requestUrl = `https://checkvip.767700.xyz?token=${encodeURIComponent(cleanUrl)}`;

                const response = await fetch(requestUrl, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
                    }
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`请求失败: ${response.status} ${response.statusText}\n${errorText}`);
                }

                const result = await response.json();

                if (result.error) {
                    throw new Error(`API 错误: ${result.error}${result.message ? ' - ' + result.message : ''}`);
                }

                if (!result.ok) {
                    throw new Error(`API 请求失败: ${result.status} ${result.statusText || result.error || '未知错误'}`);
                }

                return result.data;

            } catch (error) {
                throw error;
            }
        }

        // 显示最终 useage 总余额
        function displayPoolFinalUsageBalance(totalBalance, results) {
            const poolInfo = document.getElementById('poolAccountInfo');
            let usageDiv = poolInfo.querySelector('.pool-account-item:last-child');

            if (usageDiv && usageDiv.querySelector('.pool-account-key').textContent === 'useage:') {
                usageDiv.remove();
            }

            const newUsageDiv = document.createElement('div');
            newUsageDiv.className = 'pool-account-item';
            newUsageDiv.innerHTML = `
                <div class="pool-account-key">useage:</div>
                <div class="pool-account-value">
                    <div class="">
                        <div class="total-balance-display">
                            <strong>总余额:</strong> <span class="total-balance-amount">${totalBalance.toFixed(2)}</span>
                        </div>
                    </div>
                </div>
            `;

            poolInfo.appendChild(newUsageDiv);
        }

        // 复制到剪贴板 (Pool 版本)
        async function copyToClipboardPool(text) {
            try {
                await navigator.clipboard.writeText(text);
                showToast('复制成功！', 'success');
            } catch (err) {
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showToast('复制成功！', 'success');
            }
        }

        // 获取并转换账号数据为 arr 格式
        async function loadAccountsData() {
            try {
                const response = await fetch('https://n-account.767700.xyz');
                if (!response.ok) {
                    throw new Error(`HTTP错误! 状态: ${response.status}`);
                }

                const data = await response.json();
                if (!data.result || !Array.isArray(data.result)) {
                    throw new Error('返回数据格式错误：缺少 result 数组字段');
                }

                // 筛选并转换数据
                const newArr = data.result
                    .filter(account => {
                        const props = account.properties;
                        return props?.useage?.rich_text?.[0]?.plain_text &&
                               props?.id?.rich_text?.[0]?.plain_text;
                    })
                    .map(account => {
                        const props = account.properties;

                        // 解析 tokens
                        let tokens = [];
                        const useageText = props.useage.rich_text[0].plain_text;
                        try {
                            const parsed = JSON.parse(useageText);
                            tokens = Array.isArray(parsed) ? parsed : [parsed];
                        } catch {
                            tokens = [useageText];
                        }

                        return {
                            tokens: tokens,
                            user: props.id.rich_text[0].plain_text,
                            time: props.created_time || ''
                        };
                    });

                console.log(`成功加载 ${newArr.length} 个账号数据`);
                return newArr;

            } catch (error) {
                console.error('加载账号数据失败:', error);
                throw error;
            }
        }



        // 初始化账号数据
        async function initializeAccountsData() {
            const button = document.getElementById('testUseageBtn');

            try {
                if (button) {
                    button.disabled = true;
                    button.textContent = '加载中...';
                }

                arr = await loadAccountsData();
                console.log(arr);

                showToast(`成功加载 ${arr.length} 个账号数据`, 'success');

            } catch (error) {
                showToast(`加载失败: ${error.message}`, 'error');
                console.error('初始化账号数据失败:', error);
            } finally {
                if (button) {
                    button.disabled = false;
                    button.textContent = '🔍 获取有useage的账号';
                }
            }
        }

        // 手动刷新数据（保留原按钮功能）
        async function testGetAccountsWithUseage() {
            await initializeAccountsData();
        }

        // 页面加载完成后自动执行
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，开始自动加载账号数据...');
            initializeAccountsData();
        });


    </script>
</body>
</html>
